import os
import json
import torch
import pickle as pkl
import warnings
import pandas as pd
import numpy as np
from gensim.models import Word2Vec
from sklearn.utils import class_weight
from torch_geometric import utils
from torch_geometric.loader import NeighborLoader
from torch.nn import CrossEntropyLoss
warnings.filterwarnings('ignore')

from config import Config
from utils import *
from model import *

def transform_raw_data(cfg):
    dataset_name = cfg.dataset_name
    train_test_dirname = cfg.train_test_dirname
    raw_data_path = cfg.raw_data_path
    
    id_nodetype_map_list = []
    for dir in train_test_dirname:
        path = os.path.join(raw_data_path, dir)
        id_nodetype_map = process_data(path)  # 处理节点信息
        process_edges(path, id_nodetype_map)  # 处理边信息 -> txt文件
        id_nodetype_map_list.append(id_nodetype_map)
    os.system(f'cp {raw_data_path}//{dataset_name}//{cfg.train_test_filename[0]} .//processed_data//{dataset_name}//train.txt')
    os.system(f'cp {raw_data_path}//{dataset_name}//{cfg.train_test_filename[1]} .//processed_data//{dataset_name}//test.txt')
    with open(f'./processed_data/{dataset_name}/id_nodetype_map.pkl', 'wb') as f:
        pkl.dump({**id_nodetype_map_list[0], **id_nodetype_map_list[1]}, f)
    return {**id_nodetype_map_list[0], **id_nodetype_map_list[1]}

# def load_data():
#     f = open("test.txt")
#     data = f.read().split('\n')
#     data = [line.split('\t') for line in data]
#     df = pd.DataFrame (data, columns = ['actorID', 'actor_type','objectID','object','action','timestamp'])
#     df = df.dropna()
#     df.sort_values(by='timestamp', ascending=True,inplace=True)

def train(graph, labels, mask, model, optimizer, device):
    l = np.array(labels)
    class_weights = class_weight.compute_class_weight(class_weight = "balanced",classes = np.unique(l),y = l)
    class_weights = torch.tensor(class_weights,dtype=torch.float).to(device)
    criterion = CrossEntropyLoss(weight=class_weights,reduction='mean')

    for m_n in range(20):

      loader = NeighborLoader(graph, num_neighbors=[-1,-1], batch_size=5000,input_nodes=mask)
      total_loss = 0
      for subg in loader:
          model.train()
          optimizer.zero_grad() 
          out = model(subg.x, subg.edge_index) 
          loss = criterion(out, subg.y) 
          loss.backward() 
          optimizer.step()      
          total_loss += loss.item() * subg.batch_size
      print(total_loss / mask.sum().item())

      loader = NeighborLoader(graph, num_neighbors=[-1,-1], batch_size=5000,input_nodes=mask)
      for subg in loader:
          model.eval()
          out = model(subg.x, subg.edge_index)

          sorted, indices = out.sort(dim=1,descending=True)
          conf = (sorted[:,0] - sorted[:,1]) / sorted[:,0]
          conf = (conf - conf.min()) / conf.max()

          pred = indices[:,0]
          cond = (pred == subg.y) | (conf >= 0.9)
          mask[subg.n_id[cond]] = False

      torch.save(model.state_dict(), f'trained_weights/{dataset_name}/lword2vec_gnn_{m_n}.pth')
      print(f'Model# {m_n}. {mask.sum().item()} nodes still misclassified \n')

def test(graph, flag, model, conf_thre):
    for m_n in range(20):
        model.load_state_dict(torch.load(f'trained_weights/{dataset_name}/lword2vec_gnn_{dataset_name}{m_n}_E3.pth',map_location=torch.device('cpu')))
        loader = NeighborLoader(graph, num_neighbors=[-1,-1], batch_size=5000)    
        for subg in loader:
            model.eval()
            out = model(subg.x, subg.edge_index)

            sorted, indices = out.sort(dim=1,descending=True)
            conf = (sorted[:,0] - sorted[:,1]) / sorted[:,0]
            conf = (conf - conf.min()) / conf.max()
            
            pred = indices[:,0]
            cond = (pred == subg.y) & (conf > conf_thre)
            flag[subg.n_id[cond]] = torch.logical_and(flag[subg.n_id[cond]], torch.tensor([False]*len(flag[subg.n_id[cond]]), dtype=torch.bool))
    return flag


def main(cfg):
    global dataset_name
    dataset_name = cfg.dataset
    train_test_filename = cfg.train_test_filename
    device = cfg.device
    dummies = cfg.dummies
    mode = cfg.mode
    conf_thre = cfg.conf_thre
    
    logger = create_logger(f'./log/{dataset_name}_train.log')
    
    logger.info('run mode: ', mode)
    # 数据预处理: json -> txt   以及   id_nodetype_map
    if 'transform_raw_data' in mode:
        id_nodetype_map = transform_raw_data(cfg)
        logger.info("已处理原始数据")
    else:
        # with open(f'./processed_data/{dataset_name}/id_nodetype_map.pkl', 'rb') as f:
        #     id_nodetype_map = pkl.load(f)
        pass
    
    if not 'only_validate' in mode:  # 如果只验证，则不需要训练数据
        # 分词，后续训练word2vec并嵌入+构图
        if 'use_saved_data' in mode:
            pkl_path = f'./processed_data/{dataset_name}/train_msg.pkl'
            try:
                with open(pkl_path, 'rb') as f:
                    phrases,labels,edges,mapp = pkl.load(f)
            except:
                raise Exception("no saved data")
        else:
            json_path = os.path.join(os.path.join('F:\\DARPA_TC_E3\\data', dataset_name), train_test_filename[0])
            phrases,labels,edges,mapp, _ = prepare_attr(txt_path=f"./processed_data/{dataset_name}/train.txt", json_path=json_path, dummies=dummies)
            with open(f'./processed_data/{dataset_name}/train_msg.pkl', 'wb') as f:
                pkl.dump((phrases,labels,edges,mapp), f)
        
        # train word2vec
        if 'train_word2vec' in mode:
            logger = EpochLogger()
            saver = EpochSaver()
            logger.info("训练word2vex模型...")
            word2vec = Word2Vec(sentences=phrases, vector_size=30, window=5, min_count=1, workers=8,epochs=300,callbacks=[saver,logger])
    
    # model
    model = GCN(30,5).to(device)
    encoder = PositionalEncoder(30)  # 位置编码器
    w2vmodel = Word2Vec.load(f"trained_weights/{dataset_name}/word2vec_{dataset_name}_E3.model")
    optimizer = torch.optim.Adam(model.parameters(), lr=0.01, weight_decay=5e-4)
    
    # 嵌入+构图      训练
    if not 'only_validate' in mode:
        if 'use_saved_data' in mode:
            with open(f'./processed_data/{dataset_name}/train_graph.pkl', 'rb') as f:
                graph, mask = pkl.load(f)
        else:
            graph, mask = construct_graph(phrases, labels, edges, w2vmodel, encoder, device)
            with open(f'./processed_data/{dataset_name}/train_graph.pkl', 'wb') as f:
                pkl.dump((graph, mask), f)
        logger.info("训练GNN模型...")
        train(graph, labels, mask, model, optimizer, device)
    
    # val
    if 'use_saved_data' in mode:
        pkl_path = f'./processed_data/{dataset_name}/test_msg.pkl'
        try:
            with open(pkl_path, 'rb') as f:
                phrases,labels,edges,mapp, df = pkl.load(f)
            with open(f'./processed_data/{dataset_name}/test_graph.pkl', 'rb') as f:
                graph, flag = pkl.load(f)
        except:
            raise Exception('no saved data')    
    else:
        json_path = os.path.join(os.path.join('F:\\DARPA_TC_E3\\data', dataset_name), train_test_filename[1])
        phrases,labels,edges,mapp, df = prepare_attr(txt_path=f"./processed_data/{dataset_name}/test.txt", json_path=json_path, dummies=dummies)
        with open(f'./processed_data/{dataset_name}/test_msg.pkl', 'wb') as f:
            pkl.dump((phrases,labels,edges,mapp,df), f)        
        
        graph, flag = construct_graph(phrases, labels, edges, w2vmodel, encoder, device)
        with open(f'./processed_data/{dataset_name}/test_graph.pkl', 'wb') as f:
            pkl.dump((graph, flag), f)
    
    logger.info("开始测试...")
    flag = test(graph, flag, model, conf_thre=conf_thre)
    
    # result
    with open(f"data_files/{dataset_name}.json", "r") as json_file:
        GT_mal = set(json.load(json_file))
    all_ids = list(df['actorID']) + list(df['objectID'])
    all_ids = set(all_ids)

    index = utils.mask_to_index(flag).tolist()
    ids = set([mapp[x] for x in index])
    alerts = helper(set(ids),set(all_ids),GT_mal,edges,mapp) 
    
if __name__ == "__main__":
    cfg = Config()
    main(cfg)