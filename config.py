import torch

class PoiConfig:
    """配置类,包含所有实验参数
    """
    def __init__(self):
        # 数据集配置
        self.dataset = "theia"
        self.poison_ratio = {'SUBJECT_PROCESS':0.03, 'NetFlowObject':0.03, 'FILE_OBJECT_BLOCK':0.03}

        # 投毒节点选择
        self.max_in_degree = 10
        self.k_neighbors = 10
        
        # 训练配置
        self.epochs = 60
        self.batch_size = 5000
        self.lr_d = 0.001  # 检测器学习率
        self.lr_g = 0.001  # glubel 0.0003
        self.weight_decay = 5e-4  # 检测器 weight_decay   5e-4  5e-3
        self.optimizer = 'adam'

        self.epochs_d = 1   # 检测器训练轮数
        self.epochs_g = 2   # 生成器训练轮数

        # 触发器配置
        self.trigger_shape = (3,10)  # (进程数,文件数)
        # self.trigger_shape = (5,20)  # (进程数,文件数)
        # self.trigger_shape = (4, 15)  # (进程数,文件数)
        self.thre = 0.5  # 触发器阈值
        self.k_hop = 2  # 触发器生成器输入k阶入子图

        # GPU配置
        self.device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
        self.seed = 0
        # 学习率调度器参数
        self.lr_scheduler_step_size = 33  # 每隔多少epoch衰减一次
        self.lr_scheduler_gamma = 0.5     # 衰减系数

        # 批处理优化配置
        self.trigger_batch_size = 32  # 触发器生成批处理大小
        self.embedding_batch_size = 1024  # 嵌入计算批处理大小


class Config():
    def __init__(self) -> None:
        self.dataset = 'theia'
        self.raw_data_path = "D:\Doctor\\实验室\APT\\投毒paper\\DARPDARPA T3 数据集\data\\" + self.dataset
        self.device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
        
        train_test_dirname = {'theia':['ta1-theia-e3-official-1r.json', 'ta1-theia-e3-official-6r.json'],
                     'cadets':['ta1-cadets-e3-official.json', 'ta1-cadets-e3-official-2.json'],
                     'trace':['ta1-trace-e3-official-1.json']
                     }
        train_test_filename = {'theia':['ta1-theia-e3-official-1r.json', 'ta1-theia-e3-official-6r.json.8'],
                                'cadets':['ta1-cadets-e3-official.json', 'ta1-cadets-e3-official-2.json'],
                                'trace':['ta1-trace-e3-official-1.json']
                                }
        dummies = {'theia':{"SUBJECT_PROCESS":	0, "MemoryObject":	1, "FILE_OBJECT_BLOCK":	2,
                            "NetFlowObject":	3,"PRINCIPAL_REMOTE":	4,'PRINCIPAL_LOCAL':5},
                   'cadets':{'SUBJECT_PROCESS': 0, 'FILE_OBJECT_FILE': 1, 'FILE_OBJECT_UNIX_SOCKET': 2, 
                            'UnnamedPipeObject': 3, 'NetFlowObject': 4, 'FILE_OBJECT_DIR': 5},
                   'trace':{"SUBJECT_PROCESS":0, "MemoryObject":1, "FILE_OBJECT_CHAR":2, "FILE_OBJECT_FILE":3,
                            "FILE_OBJECT_DIR":4, "SUBJECT_UNIT":5, "UnnamedPipeObject":6, "FILE_OBJECT_UNIX_SOCKET":7, 
                            "SRCSINK_UNKNOWN":8, "FILE_OBJECT_LINK":9, "NetFlowObject":10, "FILE_OBJECT_BLOCK":11}
                   }
        
        self.train_test_dirname = train_test_dirname[self.dataset]
        self.train_test_filename = train_test_filename[self.dataset]
        self.dummies = dummies[self.dataset]
        
             
        # "only_validate",
        # "train_word2vec"
        # "use_saved_data", "transform_raw_data", 
        # self.mode = ['only_validate', 'use_saved_data']
        self.mode = ['only_validate','use_saved_data']
        
        
        conf_thre = {'theia':0.53,'cadets':0}
        self.conf_thre = conf_thre[self.dataset]
